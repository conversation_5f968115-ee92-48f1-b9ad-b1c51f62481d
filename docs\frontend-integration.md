# Frontend Integration Guide

## Overview
This guide provides implementation details for integrating the optimized API with the existing React.js frontend application.

## Enhanced API Service Layer

### 1. Updated Auth Service
```typescript
// src/api/authService.ts
import axios, { AxiosResponse } from 'axios';
import { 
  LoginDto, 
  RegisterUserDto, 
  LoginResponseDto, 
  UserProfileDto,
  ForgotPasswordDto,
  ResetPasswordDto 
} from '../types/auth.types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';

// Enhanced axios instance with interceptors
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add request ID for tracking
    config.headers['X-Request-ID'] = generateRequestId();
    config.headers['X-Client-Version'] = process.env.REACT_APP_VERSION || '1.0.0';
    
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken
          });

          const { accessToken } = response.data.data;
          localStorage.setItem('accessToken', accessToken);

          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const authService = {
  // Enhanced login with device info
  login: async (credentials: LoginDto): Promise<LoginResponseDto> => {
    const deviceInfo = {
      deviceId: getDeviceId(),
      deviceName: getDeviceName(),
      platform: getPlatform()
    };

    const response: AxiosResponse<ApiResponse<LoginResponseDto>> = await apiClient.post(
      '/auth/login',
      { ...credentials, deviceInfo }
    );

    const { user, tokens } = response.data.data;

    // Store tokens
    localStorage.setItem('accessToken', tokens.accessToken);
    localStorage.setItem('refreshToken', tokens.refreshToken);
    localStorage.setItem('user', JSON.stringify(user));

    return response.data.data;
  },

  // Enhanced registration
  register: async (userData: RegisterUserDto): Promise<void> => {
    const response: AxiosResponse<ApiResponse<void>> = await apiClient.post(
      '/auth/register',
      userData
    );
    return response.data.data;
  },

  // Logout with token cleanup
  logout: async (): Promise<void> => {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (refreshToken) {
        await apiClient.post('/auth/logout', { refreshToken });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }
  },

  // Enhanced profile fetching
  getProfile: async (): Promise<UserProfileDto> => {
    const response: AxiosResponse<ApiResponse<UserProfileDto>> = await apiClient.get(
      '/users/profile'
    );
    return response.data.data;
  },

  // Update profile
  updateProfile: async (profileData: Partial<UserProfileDto>): Promise<UserProfileDto> => {
    const response: AxiosResponse<ApiResponse<UserProfileDto>> = await apiClient.put(
      '/users/profile',
      profileData
    );
    return response.data.data;
  },

  // Password management
  forgotPassword: async (email: string): Promise<void> => {
    const response: AxiosResponse<ApiResponse<void>> = await apiClient.post(
      '/auth/forgot-password',
      { email }
    );
    return response.data.data;
  },

  resetPassword: async (resetData: ResetPasswordDto): Promise<void> => {
    const response: AxiosResponse<ApiResponse<void>> = await apiClient.post(
      '/auth/reset-password',
      resetData
    );
    return response.data.data;
  },

  // Email verification
  verifyEmail: async (token: string): Promise<void> => {
    const response: AxiosResponse<ApiResponse<void>> = await apiClient.post(
      '/auth/verify-email',
      { token }
    );
    return response.data.data;
  },

  resendVerification: async (email: string): Promise<void> => {
    const response: AxiosResponse<ApiResponse<void>> = await apiClient.post(
      '/auth/resend-verification',
      { email }
    );
    return response.data.data;
  },

  // Token management
  refreshToken: async (): Promise<string> => {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response: AxiosResponse<ApiResponse<{ accessToken: string }>> = await apiClient.post(
      '/auth/refresh',
      { refreshToken }
    );

    const { accessToken } = response.data.data;
    localStorage.setItem('accessToken', accessToken);
    return accessToken;
  },

  // Authentication status
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('accessToken');
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  },

  // Get current user from storage
  getCurrentUser: (): UserProfileDto | null => {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }
};

// Utility functions
const getDeviceId = (): string => {
  let deviceId = localStorage.getItem('deviceId');
  if (!deviceId) {
    deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('deviceId', deviceId);
  }
  return deviceId;
};

const getDeviceName = (): string => {
  const userAgent = navigator.userAgent;
  if (/iPhone/.test(userAgent)) return 'iPhone';
  if (/iPad/.test(userAgent)) return 'iPad';
  if (/Android/.test(userAgent)) return 'Android Device';
  if (/Windows/.test(userAgent)) return 'Windows PC';
  if (/Mac/.test(userAgent)) return 'Mac';
  return 'Unknown Device';
};

const getPlatform = (): string => {
  const userAgent = navigator.userAgent;
  if (/iPhone|iPad/.test(userAgent)) return 'iOS';
  if (/Android/.test(userAgent)) return 'Android';
  if (/Windows/.test(userAgent)) return 'Windows';
  if (/Mac/.test(userAgent)) return 'macOS';
  return 'Web';
};

export default authService;
```

### 2. Enhanced Type Definitions
```typescript
// src/types/auth.types.ts
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
  requestId: string;
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: Array<{
      field: string;
      message: string;
    }>;
  };
  timestamp: string;
  requestId: string;
}

export interface LoginDto {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterUserDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
}

export interface LoginResponseDto {
  user: UserDto;
  tokens: TokensDto;
}

export interface UserDto {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  status: UserStatus;
  emailVerified: boolean;
  roles: string[];
  permissions: string[];
  createdAt: string;
  lastLoginAt?: string;
}

export interface UserProfileDto {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  phoneNumber?: string;
  phoneVerified: boolean;
  dateOfBirth?: string;
  gender?: Gender;
  profilePictureUrl?: string;
  bio?: string;
  timezone: string;
  languagePreference: string;
  status: UserStatus;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

export interface TokensDto {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface ForgotPasswordDto {
  email: string;
}

export interface ResetPasswordDto {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING_VERIFICATION = 'PENDING_VERIFICATION'
}

export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
  PREFER_NOT_TO_SAY = 'PREFER_NOT_TO_SAY'
}
```

### 3. Enhanced Authentication Context
```typescript
// src/contexts/AuthContext.tsx
import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { UserProfileDto } from '../types/auth.types';
import { authService } from '../api/authService';

interface AuthState {
  user: UserProfileDto | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: UserProfileDto }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<UserProfileDto> }
  | { type: 'CLEAR_ERROR' };

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return { ...state, isLoading: true, error: null };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'AUTH_FAILURE':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'AUTH_LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

interface AuthContextType extends AuthState {
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (profileData: Partial<UserProfileDto>) => Promise<void>;
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status on app load
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async (): Promise<void> => {
    dispatch({ type: 'AUTH_START' });

    try {
      if (authService.isAuthenticated()) {
        const user = authService.getCurrentUser();
        if (user) {
          // Verify with server and get fresh user data
          const freshUser = await authService.getProfile();
          dispatch({ type: 'AUTH_SUCCESS', payload: freshUser });
        } else {
          // Token exists but no user data, fetch from server
          const user = await authService.getProfile();
          dispatch({ type: 'AUTH_SUCCESS', payload: user });
        }
      } else {
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  const login = async (email: string, password: string, rememberMe = false): Promise<void> => {
    dispatch({ type: 'AUTH_START' });

    try {
      const response = await authService.login({ email, password, rememberMe });
      dispatch({ type: 'AUTH_SUCCESS', payload: response.user as UserProfileDto });
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || error.message || 'Login failed';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw error;
    }
  };

  const register = async (userData: any): Promise<void> => {
    dispatch({ type: 'AUTH_START' });

    try {
      await authService.register(userData);
      // Don't auto-login after registration, user needs to verify email
      dispatch({ type: 'AUTH_LOGOUT' });
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || error.message || 'Registration failed';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  const updateProfile = async (profileData: Partial<UserProfileDto>): Promise<void> => {
    try {
      const updatedUser = await authService.updateProfile(profileData);
      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || error.message || 'Profile update failed';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw error;
    }
  };

  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    updateProfile,
    clearError,
    checkAuthStatus,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
```
