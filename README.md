# Sample Auth App - Optimized Database Design & API Contracts

## Overview

This repository contains a comprehensive, production-ready authentication system with an optimized database design and modular API architecture. The project demonstrates modern best practices for building scalable authentication systems with React.js frontend and Node.js backend.

## 🏗️ Architecture Overview

### Frontend Stack
- **React 18** with TypeScript
- **Material-UI v5** for UI components
- **React Hook Form** for form management
- **React Router v6** for navigation
- **Axios** for HTTP requests
- **Context API** for state management

### Backend Stack (Recommended)
- **Node.js** with Express.js or NestJS
- **PostgreSQL 14+** for primary database
- **Redis** for caching and session management
- **Prisma** or TypeORM for database ORM
- **JWT** with refresh tokens for authentication
- **bcrypt** for password hashing

## 📋 Features

### Current Features
- ✅ User registration and login
- ✅ Password reset functionality
- ✅ Email verification
- ✅ Protected routes
- ✅ Form validation with Yup
- ✅ Material-UI components
- ✅ Responsive design

### Enhanced Features (Proposed)
- 🔐 **Advanced Security**
  - Two-factor authentication (2FA)
  - Account lockout after failed attempts
  - Session management
  - Security event logging
  
- 👤 **User Management**
  - Comprehensive user profiles
  - Address management
  - User preferences
  - Profile picture uploads
  
- 🛡️ **Role-Based Access Control (RBAC)**
  - Flexible role and permission system
  - Admin panel for user management
  - Resource-based permissions
  
- 📊 **Audit & Monitoring**
  - User activity logging
  - Security event tracking
  - Performance monitoring
  - Health check endpoints

## 📁 Project Structure

```
sample-auth-app/
├── docs/                          # Documentation
│   ├── database-design.md         # Database schema and design
│   ├── api-contracts.md           # API endpoint specifications
│   ├── data-models.md             # Data models and DTOs
│   ├── implementation-guide.md    # Backend implementation guide
│   ├── frontend-integration.md    # Frontend integration guide
│   └── deployment-security.md     # Deployment and security guide
├── src/                           # Frontend source code
│   ├── api/                       # API service layer
│   ├── components/                # Reusable components
│   ├── pages/                     # Page components
│   ├── contexts/                  # React contexts
│   └── types/                     # TypeScript type definitions
├── backend/                       # Backend source code (to be implemented)
│   ├── src/
│   │   ├── controllers/           # Route controllers
│   │   ├── services/              # Business logic
│   │   ├── middleware/            # Express middleware
│   │   ├── models/                # Data models
│   │   ├── dto/                   # Data transfer objects
│   │   └── utils/                 # Utility functions
│   ├── prisma/                    # Database schema and migrations
│   └── docker/                    # Docker configuration
└── README.md
```

## 🗄️ Database Design

The optimized database design includes:

### Core Tables
- **users** - User authentication data
- **user_profiles** - User profile information
- **user_addresses** - User address management
- **user_preferences** - User settings and preferences

### Security Tables
- **refresh_tokens** - JWT refresh token management
- **password_reset_tokens** - Password reset functionality
- **email_verification_tokens** - Email verification system

### RBAC Tables
- **roles** - System roles
- **permissions** - Granular permissions
- **role_permissions** - Role-permission mapping
- **user_roles** - User role assignments

### Audit Tables
- **user_activity_logs** - User action tracking
- **security_events** - Security-related events

For detailed schema information, see [Database Design Documentation](docs/database-design.md).

## 🔌 API Contracts

The API follows RESTful principles with comprehensive endpoints for:

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Token refresh
- `POST /auth/forgot-password` - Password reset request
- `POST /auth/reset-password` - Password reset confirmation

### User Management
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `POST /users/profile/picture` - Upload profile picture
- `PUT /users/password` - Change password
- `GET /users/addresses` - Get user addresses
- `POST /users/addresses` - Add user address

### Security
- `GET /users/sessions` - Get active sessions
- `DELETE /users/sessions/{id}` - Revoke session
- `POST /users/2fa/enable` - Enable 2FA
- `POST /users/2fa/verify` - Verify 2FA

For complete API documentation, see [API Contracts Documentation](docs/api-contracts.md).

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis (optional, for production)

### Frontend Setup
```bash
# Clone the repository
git clone <repository-url>
cd sample-auth-app

# Install dependencies
npm install

# Start development server
npm start
```

### Backend Setup (To be implemented)
```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Set up database
npx prisma migrate dev
npx prisma db seed

# Start development server
npm run dev
```

### Docker Setup
```bash
# Start all services with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f
```

## 🔧 Configuration

### Environment Variables

#### Frontend (.env)
```bash
REACT_APP_API_URL=http://localhost:8080/api/v1
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development
```

#### Backend (.env)
```bash
DATABASE_URL=postgresql://username:password@localhost:5432/sample_auth_app
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secure-jwt-secret
JWT_REFRESH_SECRET=your-super-secure-refresh-secret
EMAIL_SERVICE=sendgrid
SENDGRID_API_KEY=your-sendgrid-api-key
```

For complete configuration details, see [Deployment & Security Guide](docs/deployment-security.md).

## 🧪 Testing

### Frontend Testing
```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

### Backend Testing
```bash
# Run unit tests
npm run test

# Run integration tests
npm run test:integration

# Run all tests with coverage
npm run test:coverage
```

## 📚 Documentation

Comprehensive documentation is available in the `docs/` directory:

1. **[Database Design](docs/database-design.md)** - Complete database schema and design principles
2. **[API Contracts](docs/api-contracts.md)** - Detailed API endpoint specifications
3. **[Data Models](docs/data-models.md)** - TypeScript interfaces and DTOs
4. **[Implementation Guide](docs/implementation-guide.md)** - Backend implementation details
5. **[Frontend Integration](docs/frontend-integration.md)** - Frontend integration patterns
6. **[Deployment & Security](docs/deployment-security.md)** - Production deployment guide

## 🔒 Security Features

- **Password Security**: bcrypt hashing with salt
- **JWT Authentication**: Access and refresh token pattern
- **Rate Limiting**: Configurable rate limits for different endpoints
- **Input Validation**: Comprehensive validation and sanitization
- **CORS Protection**: Configurable CORS policies
- **Security Headers**: Helmet.js security headers
- **Account Security**: Failed login tracking and account lockout
- **Audit Logging**: Comprehensive security event logging

## 🚀 Deployment

### Production Deployment
1. Set up production environment variables
2. Configure database with SSL
3. Set up Redis for session management
4. Configure email service (SendGrid/AWS SES)
5. Set up file storage (AWS S3/CloudFlare R2)
6. Deploy with Docker or your preferred platform

### Health Checks
- `GET /health` - Basic health check
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Material-UI team for the excellent component library
- React Hook Form for the powerful form management
- Prisma team for the excellent ORM
- The open-source community for the amazing tools and libraries

---

For detailed implementation instructions and best practices, please refer to the documentation in the `docs/` directory.
