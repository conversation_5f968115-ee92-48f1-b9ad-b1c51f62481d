# API Contract Specifications

## Overview
This document defines the RESTful API contracts for the sample-auth-app with comprehensive authentication, user management, and security features.

## Table of Contents
1. [Base Configuration](#base-configuration)
2. [Authentication Endpoints](#authentication-endpoints)
3. [User Profile Endpoints](#user-profile-endpoints)
4. [User Preferences Endpoints](#user-preferences-endpoints)
5. [User Address Endpoints](#user-address-endpoints)
6. [Security & Account Management](#security--account-management-endpoints)
7. [Admin Endpoints](#admin-endpoints-role-based-access)
8. [Error Codes Reference](#error-codes-reference)
9. [Security Considerations](#security-considerations)

## Base Configuration

### Base URL
```
Production: https://api.yourapp.com/v1
Development: http://localhost:8080/api/v1
```

### Common Headers
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
X-Request-ID: {unique_request_id}
X-Client-Version: {client_version}
```

### Standard Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

## Authentication Endpoints

### 1. User Registration
```http
POST /auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "acceptTerms": true,
  "marketingConsent": false
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "status": "PENDING_VERIFICATION",
      "createdAt": "2024-01-15T10:30:00Z"
    },
    "message": "Registration successful. Please check your email for verification."
  }
}
```

### 2. User Login
```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "rememberMe": false,
  "deviceInfo": {
    "deviceId": "device-uuid",
    "deviceName": "iPhone 12",
    "platform": "iOS"
  }
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "status": "ACTIVE",
      "roles": ["USER"],
      "permissions": ["profile:read", "profile:update"]
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 3600,
      "tokenType": "Bearer"
    }
  }
}
```

### 3. Token Refresh
```http
POST /auth/refresh
```

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 3600,
    "tokenType": "Bearer"
  }
}
```

### 4. Logout
```http
POST /auth/logout
```

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "logoutFromAllDevices": false
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### 5. Forgot Password
```http
POST /auth/forgot-password
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Password reset instructions sent to your email"
}
```

### 6. Reset Password
```http
POST /auth/reset-password
```

**Request Body:**
```json
{
  "token": "reset-token-123",
  "newPassword": "NewSecurePassword123!",
  "confirmPassword": "NewSecurePassword123!"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

### 7. Email Verification
```http
POST /auth/verify-email
```

**Request Body:**
```json
{
  "token": "verification-token-123"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Email verified successfully"
}
```

### 8. Resend Verification Email
```http
POST /auth/resend-verification
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Verification email sent"
}
```

## User Profile Endpoints

### 1. Get User Profile
```http
GET /users/profile
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "uuid-123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "displayName": "John Doe",
    "phoneNumber": "+1234567890",
    "phoneVerified": false,
    "dateOfBirth": "1990-01-15",
    "gender": "MALE",
    "profilePictureUrl": "https://cdn.example.com/profile.jpg",
    "bio": "Software developer",
    "timezone": "America/New_York",
    "languagePreference": "en",
    "status": "ACTIVE",
    "emailVerified": true,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "lastLoginAt": "2024-01-15T10:30:00Z"
  }
}
```

### 2. Update User Profile
```http
PUT /users/profile
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "displayName": "John Doe",
  "phoneNumber": "+1234567890",
  "dateOfBirth": "1990-01-15",
  "gender": "MALE",
  "bio": "Software developer and tech enthusiast",
  "timezone": "America/New_York",
  "languagePreference": "en"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "uuid-123",
    "firstName": "John",
    "lastName": "Doe",
    "displayName": "John Doe",
    "updatedAt": "2024-01-15T10:30:00Z"
  },
  "message": "Profile updated successfully"
}
```

### 3. Upload Profile Picture
```http
POST /users/profile/picture
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**Request Body:**
```
file: [binary data]
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "profilePictureUrl": "https://cdn.example.com/profile-new.jpg"
  },
  "message": "Profile picture updated successfully"
}
```

### 4. Change Password
```http
PUT /users/password
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "currentPassword": "CurrentPassword123!",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

### 5. Update Email
```http
PUT /users/email
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "newEmail": "<EMAIL>",
  "password": "CurrentPassword123!"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Email update initiated. Please verify your new email address."
}
```

## User Preferences Endpoints

### 1. Get User Preferences
```http
GET /users/preferences
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "notifications": {
      "email": true,
      "push": false,
      "sms": false
    },
    "privacy": {
      "profileVisibility": "PUBLIC",
      "showEmail": false,
      "showPhone": false
    },
    "theme": {
      "mode": "DARK",
      "primaryColor": "#1976d2"
    }
  }
}
```

### 2. Update User Preferences
```http
PUT /users/preferences
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "notifications": {
    "email": true,
    "push": true,
    "sms": false
  },
  "privacy": {
    "profileVisibility": "PRIVATE",
    "showEmail": false,
    "showPhone": false
  },
  "theme": {
    "mode": "LIGHT",
    "primaryColor": "#1976d2"
  }
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Preferences updated successfully"
}
```

## User Address Endpoints

### 1. Get User Addresses
```http
GET /users/addresses
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "addr-uuid-123",
      "type": "HOME",
      "isPrimary": true,
      "streetAddress1": "123 Main St",
      "streetAddress2": "Apt 4B",
      "city": "New York",
      "stateProvince": "NY",
      "postalCode": "10001",
      "countryCode": "US",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 2. Add User Address
```http
POST /users/addresses
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "type": "HOME",
  "isPrimary": true,
  "streetAddress1": "123 Main St",
  "streetAddress2": "Apt 4B",
  "city": "New York",
  "stateProvince": "NY",
  "postalCode": "10001",
  "countryCode": "US"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "id": "addr-uuid-123",
    "type": "HOME",
    "isPrimary": true,
    "streetAddress1": "123 Main St",
    "streetAddress2": "Apt 4B",
    "city": "New York",
    "stateProvince": "NY",
    "postalCode": "10001",
    "countryCode": "US",
    "createdAt": "2024-01-15T10:30:00Z"
  },
  "message": "Address added successfully"
}
```

### 3. Update User Address
```http
PUT /users/addresses/{addressId}
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "type": "WORK",
  "isPrimary": false,
  "streetAddress1": "456 Business Ave",
  "city": "New York",
  "stateProvince": "NY",
  "postalCode": "10002",
  "countryCode": "US"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "addr-uuid-123",
    "type": "WORK",
    "isPrimary": false,
    "streetAddress1": "456 Business Ave",
    "city": "New York",
    "stateProvince": "NY",
    "postalCode": "10002",
    "countryCode": "US",
    "updatedAt": "2024-01-15T10:30:00Z"
  },
  "message": "Address updated successfully"
}
```

### 4. Delete User Address
```http
DELETE /users/addresses/{addressId}
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Address deleted successfully"
}
```

## Security & Account Management Endpoints

### 1. Get Active Sessions
```http
GET /users/sessions
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "session-uuid-123",
      "deviceInfo": {
        "deviceId": "device-uuid",
        "deviceName": "iPhone 12",
        "platform": "iOS"
      },
      "ipAddress": "***********",
      "userAgent": "Mozilla/5.0...",
      "isCurrent": true,
      "createdAt": "2024-01-15T10:30:00Z",
      "lastActivity": "2024-01-15T10:30:00Z",
      "expiresAt": "2024-01-16T10:30:00Z"
    }
  ]
}
```

### 2. Revoke Session
```http
DELETE /users/sessions/{sessionId}
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Session revoked successfully"
}
```

### 3. Revoke All Sessions
```http
DELETE /users/sessions
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "All sessions revoked successfully"
}
```

### 4. Get Security Events
```http
GET /users/security-events?page=1&limit=20
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "events": [
      {
        "id": "event-uuid-123",
        "eventType": "LOGIN_SUCCESS",
        "severity": "LOW",
        "ipAddress": "***********",
        "userAgent": "Mozilla/5.0...",
        "details": {
          "location": "New York, NY",
          "device": "iPhone 12"
        },
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3
    }
  }
}
```

### 5. Enable Two-Factor Authentication
```http
POST /users/2fa/enable
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "secret": "JBSWY3DPEHPK3PXP",
    "backupCodes": [
      "12345678",
      "87654321",
      "11223344"
    ]
  },
  "message": "Scan the QR code with your authenticator app"
}
```

### 6. Verify Two-Factor Authentication
```http
POST /users/2fa/verify
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "code": "123456"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Two-factor authentication enabled successfully"
}
```

### 7. Disable Two-Factor Authentication
```http
POST /users/2fa/disable
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "password": "CurrentPassword123!",
  "code": "123456"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Two-factor authentication disabled successfully"
}
```

## Admin Endpoints (Role-Based Access)

### 1. Get All Users (Admin Only)
```http
GET /admin/users?page=1&limit=20&status=ACTIVE&search=john
Authorization: Bearer {admin_token}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "uuid-123",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "status": "ACTIVE",
        "emailVerified": true,
        "roles": ["USER"],
        "createdAt": "2024-01-15T10:30:00Z",
        "lastLoginAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### 2. Update User Status (Admin Only)
```http
PUT /admin/users/{userId}/status
Authorization: Bearer {admin_token}
```

**Request Body:**
```json
{
  "status": "SUSPENDED",
  "reason": "Policy violation"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "User status updated successfully"
}
```

### 3. Assign Role to User (Admin Only)
```http
POST /admin/users/{userId}/roles
Authorization: Bearer {admin_token}
```

**Request Body:**
```json
{
  "roleId": "role-uuid-123",
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Role assigned successfully"
}
```

## Error Codes Reference

### Authentication Errors
- `AUTH_001`: Invalid credentials
- `AUTH_002`: Account locked
- `AUTH_003`: Account not verified
- `AUTH_004`: Token expired
- `AUTH_005`: Invalid token
- `AUTH_006`: Two-factor authentication required

### Validation Errors
- `VAL_001`: Invalid email format
- `VAL_002`: Password too weak
- `VAL_003`: Required field missing
- `VAL_004`: Invalid phone number format
- `VAL_005`: Invalid date format

### Authorization Errors
- `AUTHZ_001`: Insufficient permissions
- `AUTHZ_002`: Resource not found
- `AUTHZ_003`: Access denied

### Rate Limiting
- `RATE_001`: Too many requests
- `RATE_002`: Login attempts exceeded

## HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource already exists
- `422 Unprocessable Entity`: Validation failed
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## Security Considerations

### 1. Authentication
- JWT tokens with short expiration (15 minutes for access tokens)
- Refresh tokens with longer expiration (7 days)
- Secure token storage and transmission

### 2. Authorization
- Role-based access control (RBAC)
- Permission-based resource access
- Resource ownership validation

### 3. Data Protection
- Password hashing with salt
- Sensitive data encryption
- PII data handling compliance

### 4. Rate Limiting
- Login attempts: 5 per 15 minutes
- Password reset: 3 per hour
- API requests: 1000 per hour per user

### 5. Audit Logging
- All authentication events
- Permission changes
- Data access logs
- Security events tracking
